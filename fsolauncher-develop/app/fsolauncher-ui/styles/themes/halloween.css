.halloween {
}

.halloween #logo:after {
  content: 'HALLOWEEN';
}
/*.halloween button,
.halloween .button {
  background-color: #ff691f;
  color: #15202b;
}*/

.halloween button:hover,
.halloween .button:hover {
}

.halloween #logo {
  background-image: url(../../images/logo_halloween.png) !important;
}

/*.halloween #launcher #sidebar li.active,
.halloween #launcher #sidebar li:not(#logo):hover {
  color: #ff691f;
  border-right-color: #ff691f;
}

.halloween #launcher #sidebar li:not(#logo):hover path,
.halloween #launcher #sidebar li.active path {
  fill: #ff691f;
}

.halloween .hint {
  background-color: #ff691f;
}

.halloween .hint.arrow-left:before {
  border-right-color: #ff691f;
}

.halloween .hint.arrow-top:before {
  border-bottom-color: #ff691f;
}*/

/*.halloween .loading .progress {
  background-image: -webkit-linear-gradient(
      -45deg,
      transparent 33%,
      rgba(255, 255, 255, 0.3) 33%,
      rgba(255, 255, 255, 0.3) 66%,
      transparent 66%
    ),
    -webkit-linear-gradient(top, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1)),
    -webkit-linear-gradient(left, #ff691f, #ff691f);
}*/

/*.halloween .hint.arrow-top:after {
  border-bottom-color: #ff691f;
}

.halloween .hint.arrow-left:after {
  border-right-color: #ff691f;
}*/

/*
.halloween #settings-page .form h1,
.halloween #downloads > div .tag h1,
.halloween .modal h1 {
  color: #ff691f;
}*/

/*.halloween #settings-page .form h1 {
  border-color: #ff691f !important;
}*/

.halloween .download {
  background: rgba(0, 0, 0, 0.3) !important;
  color: #fff !important;
}

.halloween .download h1 {
  color: #4C8DEE !important;
}

.halloween .download .loading {
  background: rgba(255, 255, 255, 0.2) !important;
}

.halloween .download span {
  color: rgba(255, 255, 255, 0.75) !important;
}

/*.halloween .jumbotron span {
  color: #ff691f !important;
}*/

/*.halloween input,
.halloween select {
  border-color: transparent !important;
  font-weight: bold;
  color: #15202b !important;
  background: url(../../images/15xvbd5.png), #ff691f;
  background-position: 97% center;
  background-repeat: no-repeat;
}*/

.halloween #settings-page .form div small {
  color: rgba(255, 255, 255, 0.7);
}

.halloween .item[install='FSO'] {
  background-image: url(../../images/freeso_logo.png) !important;
}

.halloween #widget {
  /*background: #10171e !important;*/
}

.halloween .modal {
  background: -webkit-linear-gradient(#15202b, #10171e 5%, #15202b) !important;
  color: rgba(255, 255, 255, 0.5) !important;
}

.halloween .tag {
  background-image: -webkit-linear-gradient(#15202b, #10171e 100%, #15202b)!important;
  color: rgba(255, 255, 255, 0.5) !important;
  border: 1px solid #414141;
}
.halloween #settings-page .form h1 {
}
.halloween #titlebar {
  background-color: #10171e;
}
.halloween #sidebar {
  background: #10171e !important;
  color: rgba(255, 255, 255, 0.75) !important;
  text-shadow: 2px 2px rgba(0, 0, 0, 0.2) !important;
}
.halloween .page-content {
  border-left: 1px solid #414141;
}
.halloween #content,
.halloween #launcher {
  background: #10171e !important;
}
.halloween .item {
  color: #fff !important;
  border-color: #414141 !important;
  background-color: #10171e !important;
}
.halloween h1.jumbotron {
  /*background: -webkit-linear-gradient(#15202b, #10171e 5%, #15202b)
  !important;*/
  background-image: -webkit-linear-gradient(#15202b, #10171e 100%, #15202b)!important;
  color: rgba(255, 255, 255, 0.75) !important;
  border: 1px solid #414141;
}

/*.halloween button.dark-transparent,
.halloween .button.dark-transparent {
  background: #ff691f;
}

.halloween button.dark-transparent:hover,
.halloween .button.dark-transparent:hover {
  background: #ff691f;
}*/

.halloween ::-webkit-scrollbar {
  width: 5px;
  padding: 3px;
}

/*.halloween ::-webkit-scrollbar-thumb {
  background: #ff691f;
}*/

.halloween ::-webkit-scrollbar-thumb:active {
  background: #080b0f;
}

.halloween ::-webkit-scrollbar-track {
  background: #15202b;
}

.halloween .notification {
  background: rgba(0, 0, 0, 0.3) !important;
}

.halloween .notification h1,
.halloween .notification i {
  color: #4C8DEE !important;
}
.halloween .notification p {
  color: rgba(255, 255, 255, 0.75) !important;
}

.halloween #installer-page .page-content {
  background-color: #10161e !important;
}

.halloween #simtime-container {
  color:rgba(255,255,255,0.5);
  background:rgba(0,0,0,0.3)
}
.halloween #sidebar li {
  color:rgba(255,255,255,0.6);
}
.halloween h1.jumbotron span {
  color: #4C8DEE;
  text-shadow: 2px 2px rgba(0, 0, 0, 0.2) !important;
}
.halloween h1.jumbotron > small {
  color:rgba(255,255,255,0.6)
}

.halloween button,
.halloween .button {
  color: #15202B;
}

.halloween div.stag {
  color: #10171e;
}

.halloween .modal {
  border: 1px solid #414141;
}

.halloween input,
.halloween select {
  background-image: -webkit-linear-gradient(#15202b, #10171e 95%, #15202b);
  font-weight: bold;
  color:rgba(255,255,255,0.7) !important;
  border-color:#414141
}

.halloween select {
  background-image: url(../../images/15xvbd5.png), 
  -webkit-linear-gradient(#15202b, #10171e 95%, #15202b);
}

.halloween #home-loading {
  color: rgba(255, 255, 255, 0.6);
}

.halloween option {
  background: #10171e;
}

.halloween .alt-content > * {
  color:rgba(255,255,255,0.6)!important;
  text-shadow: 2px 2px rgba(0, 0, 0, 0.2) !important;
}

.halloween .simitone-error {
  color:rgba(255,255,255,0.75)!important
}

/*.halloween .tag .tick.installed {
  color:#10171e!important;
}*/

.halloween #remeshinfo {
  color:rgba(255,255,255,0.6)!important
}

.halloween ::-webkit-scrollbar-thumb {
  background: #4C8DEE;
}

.halloween ::-webkit-scrollbar-thumb:active {
  background: #6397e6;
}

.halloween #settings-page .form div span {
  color: rgba(255,255,255,0.6)
}

.halloween #simitone-install-button {
  color:#fff!important;
  background: linear-gradient(
    45deg,
    #2d6e96 0%,
    #1b9a88 50%,
    #05d474 100%
  ) !important;
  text-shadow: 2px 2px rgba(0, 0, 0, 0.2) !important;
  transition: background-position 1s !important;
  background-size: 150% !important;
}

.halloween #launcher #sidebar li.active,
.halloween #launcher #sidebar li:not(#logo):hover {
  background-color: rgba(0, 0, 0, 0.2);
}

.halloween #widget {
  border-left: 1px solid #414141;
}

.halloween #sidebar div.bottom li:last-child:after {
  background-color:#414141;
}


.halloween button,
.halloween .button {
  background-image: linear-gradient(to right, rgb(255, 165, 0), rgb(255, 69, 0));
  color: #10171E;
}

.halloween #launcher #sidebar li.active,
.halloween #launcher #sidebar li:not(#logo):hover {
  color: #ff691f;
  border-right-color: #ff691f;
}

.halloween #launcher #sidebar li:not(#logo):hover path,
.halloween #launcher #sidebar li.active path {
  fill: #ff691f;
}

.halloween .hint {
  background-color: #ff691f;
}

.halloween .hint.arrow-left:before {
  border-right-color: #ff691f;
}

.halloween .hint.arrow-top:before {
  border-bottom-color: #ff691f;
}

.halloween .loading .progress {
  background-image: -webkit-linear-gradient(
      -45deg,
      transparent 33%,
      rgba(255, 255, 255, 0.3) 33%,
      rgba(255, 255, 255, 0.3) 66%,
      transparent 66%
    ),
    -webkit-linear-gradient(top, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1)),
    -webkit-linear-gradient(left, #ff691f, #ff691f);
}

.halloween .hint.arrow-top:after {
  border-bottom-color: #ff691f;
}

.halloween .hint.arrow-left:after {
  border-right-color: #ff691f;
}

.halloween .article-title,
.halloween #settings-page .form h1,
.halloween #downloads > div .tag h1,
.halloween .modal h1 {
  color: #ff691f;
  text-shadow: 2px 2px rgba(0, 0, 0, 0.2) !important;
}

.halloween #settings-page .form h1 {
  border-color: #ff691f !important;
}

.halloween .download h1 {
  color: #ff691f !important;
}

.halloween .jumbotron span {
  color: #ff691f !important;
}

.halloween .article-image::after,
.halloween button.halloween-transparent,
.halloween .button.halloween-transparent {
  background: #ff691f;
}

.halloween button.halloween-transparent:hover,
.halloween .button.halloween-transparent:hover {
  background: #ff691f;
}

.halloween ::-webkit-scrollbar {
  width: 5px;
  padding: 3px;
}

.halloween ::-webkit-scrollbar-thumb {
  background: #ff691f;
}

.halloween .notification h1,
.halloween .notification i {
  color: #ff691f !important;
}

.halloween .hint {
  color: #10171E!important
}

.halloween #downloads > div > .item[install='Mono'] {
  background-color: #10171E!important;
}

.halloween #simitone-install-button {
  color:#fff!important;
  background: linear-gradient(
    45deg,
    #2d6e96 0%,
    #1b9a88 50%,
    #05d474 100%
  ) !important;
  text-shadow: 2px 2px rgba(0, 0, 0, 0.2) !important;
  transition: background-position 1s !important;
  background-size: 150% !important;
}

.halloween #simitone-play-button {
  color:#fff!important;
  background: linear-gradient(
    45deg,
    #2d6e96 0%,
    #1b9a88 50%,
    #05d474 100%
  ) !important;
  text-shadow: 2px 2px rgba(0, 0, 0, 0.2) !important;
  transition: background-position 1s !important;
  background-size: 150% !important;
}

.halloween #simitone-should-update {
  color: #10171E!important;
}

.halloween .download.stopped .loading .progress {
  background-image: none;
  background: #FF691F;
}

.halloween #launcher #sidebar li.active:before,
.halloween #launcher #sidebar li:not(#logo):hover:before {
  background: #FF691F!important;
}


.halloween #launcher #content #full-install {
  background-image: url(../../images/citydark.jpg);
} 


.halloween .oneclick-install {
  background-image: linear-gradient(90deg, rgb(50 136 195 / 1) 0%, #44ABED 50.31%, #5BD536 100%);
}
.halloween .oneclick-install-content {
  background: -webkit-linear-gradient(#15202b, #10171e 5%, #0e1216) !important;
}
.halloween .oneclick-install-content p {
  text-shadow:  2px rgb(0 0 0 / 20%);
    color: rgba(255, 255, 255, 0.5);
}
.halloween .oneclick-install-select {
  border-color: rgba(255, 255, 255, 0.5) !important;
}
.halloween .oneclick-install-select svg {
  filter: drop-shadow( 1px 1px 1px rgba(0, 0, 0, .5));
}
.halloween .oneclick-install-select:hover p {
  color: rgba(255, 255, 255, 0.7);
}

.halloween .oneclick-install .oneclick-install-select button {
  color: #10171E;
}