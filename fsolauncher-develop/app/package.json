{"name": "n<PERSON>auncher", "productName": "NewSO Launcher", "version": "1.12.1", "description": "Install, configure, and launch NewSO with ease", "main": "main.js", "scripts": {"start": "electron .", "test": "playwright test -x", "debug": "electron --inspect=5858 .", "devtools": "electron . --inspect=5858 --fl-devtools --disable-gpu-sandbox --enable-logging --v=1", "filelog": "electron . --fl-filelog", "lint": "eslint . --ext .js", "startiss": "cmd /K \"../release/NewSO Launcher Setup.exe\"", "compileiss": "node scripts/update-iss-version.js && innosetup-compiler --verbose ../release/win32-ia32.iss", "copywin": "cpy \"./**/*\" ../../release/nsolauncher-win32-ia32 --cwd=../extras/fsolauncher-build-extras --parents", "buildwin": "node scripts/build-win.js", "builddarwin": "npm run rebuild && node scripts/build-darwin.js", "builddeb": "node scripts/build-debian.js", "postinstall": "export PYTHON=/opt/homebrew/opt/python@3.13/bin/python3.13 && export CXXFLAGS=\"-std=c++20\" && npx electron-builder install-app-deps", "rebuild": "export PYTHON=/opt/homebrew/opt/python@3.13/bin/python3.13 && export CXXFLAGS=\"-std=c++20\" && npx electron-rebuild"}, "keywords": ["NewSO", "launcher"], "author": "ItsSim&Professor Oak", "devDependencies": {"@electron/packager": "^18.3.1", "@playwright/test": "^1.36.1", "cpy-cli": "^3.1.1", "electron": "25.8.0", "electron-builder": "^24.9.1", "electron-playwright-helpers": "^1.6.0", "electron-rebuild": "^3.2.9", "eslint": "^7.32.0", "playwright": "^1.36.1"}, "optionalDependencies": {"electron-installer-debian": "^3.2.0", "electron-installer-dmg": "4.0.0", "innosetup-compiler": "^6.2.0"}, "dependencies": {"@sentry/electron": "^4.0.0", "deflate-js": "^0.2.3", "dompurify": "^3.0.9", "electron-custom-notifications": "file:../vendor/electron-custom-notifications", "fix-path": "^3.0.0", "follow-redirects": "^1.15.4", "fs-extra": "^10.1.0", "howler": "^2.2.4", "ini": "^2.0.0", "macos-alias": "^0.2.11", "mime": "^3.0.0", "os-locale": "^5.0.0", "pug": "^3.0.2", "socket.io-client": "^4.7.4", "sudo-prompt": "^9.2.1", "v8-compile-cache": "^2.3.0", "yauzl": "^2.10.0"}}