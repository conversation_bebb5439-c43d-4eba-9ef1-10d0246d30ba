const path = require('path');
const os = require('os');
const packageJson = require('../package.json');

// Override console.log to replace undefined with "Reticulating splines..."
const originalConsoleLog = console.log;
console.log = function() {
    const args = Array.from(arguments).map(arg => {
        if (arg === undefined || (typeof arg === 'string' && arg.includes('undefined'))) {
            return "Reticulating splines...";
        }
        return arg;
    });
    originalConsoleLog.apply(console, args);
};

// Define base paths
const homeDir = os.homedir();
const appData = process.env.APPDATA || (process.platform === 'darwin' ? process.env.HOME + '/Library/Preferences' : process.env.HOME + '/.local/share');
const linuxLibPath = process.platform === 'linux' ? '/usr/lib/x86_64-linux-gnu' : '';
const isTestMode = process.argv.indexOf('--fl-test-mode') !== -1;
const fileLogEnabled = process.argv.indexOf('--fl-filelog') !== -1;
const devToolsEnabled = process.argv.indexOf('--fl-devtools') !== -1;
const version = packageJson.version;
const defaultRefreshRate = 60;
const defaultGameLanguage = 'English';

const dependencies = {
  'nSO': ['nSO', ...(['darwin', 'linux'].includes(process.platform) ? ['Mono', 'SDL'] : ['OpenAL'])],
  'RMS': ['NSO'],
  'MacExtras': ['NSO'],
  'Simitone': (['darwin', 'linux'].includes(process.platform)) ? ['Mono', 'SDL'] : []
};

const needInternet = [
  'TSO',
  'nSO',
  'RMS',
  'Simitone',
  'Mono',
  'MacExtras',
  'SDL'
];

const darkThemes = [
  'halloween', 'dark', 'indigo'
];

const components = {
  'TSO': 'The Sims Online',
  'NSO': 'NewSO',
  'OpenAL': 'OpenAL',
  'NET': '.NET Framework',
  'RMS': 'Remesh Package',
  'Simitone': 'Simitone for Windows',
  'Mono': 'Mono Runtime',
  'MacExtras': 'FreeSO MacExtras',
  'SDL': 'SDL2'
};

const versionChecks = {
  remeshPackageUrl: 'https://beta.freeso.org/RemeshPackage',
  updatesUrl: 'https://beta.freeso.org/UpdateCheck',
  interval: 5 * 60 * 1000 // every 5 minutes
};

const links = {
  updateWizardUrl: 'https://beta.freeso.org/update',
  repoNewIssueUrl: 'https://github.com/ItsSim/fsolauncher/issues/new/choose',
  repoViewIssuesUrl: 'https://github.com/ItsSim/fsolauncher/issues',
  repoDocsUrl: 'https://github.com/ItsSim/fsolauncher/wiki',
  repoUrl: 'https://github.com/ItsSim/fsolauncher'
};

const releases = {
  simitoneUrl: 'https://api.github.com/repos/riperiperi/Simitone/releases/latest',
  fsoGithubUrl: 'https://nsoupdates.s3.us-east-2.amazonaws.com/updates/TheNewSimsOnline_latest.zip',
  fsoApiUrl: 'https://newso.thenewsimsonline.com'
};

const resourceCentral = {
  'TheSimsOnline': 'https://newso.thenewsimsonline.com/tso',
  'FreeSO': 'https://beta.freeso.org/LauncherResourceCentral/FreeSO',
  'NewSO': 'https://newso.thenewsimsonline.com/nso',
  'OpenAL': 'https://openal.org/downloads/oalinst.exe',
  '3DModels': 'https://newso.thenewsimsonline.com/3d',
  'Mono': 'https://beta.freeso.org/LauncherResourceCentral/Mono',
  'MacExtras': 'https://newso.thenewsimsonline.com/MacExtras',
  'SDL': 'https://beta.freeso.org/LauncherResourceCentral/SDL',
  'WS': 'https://beta.freeso.org/LauncherResourceCentral/ws',
  'TrendingLots': 'https://newso.thenewsimsonline.com/userapi/city/1/lots/online',
  'Scenarios': 'https://beta.freeso.org/LauncherResourceCentral/Scenarios',
  'Blog': 'http://localhost:30631/blog',
  'NET': 'https://download.microsoft.com/download/C/3/A/C3A5200B-D33C-47E9-9D70-2F7C65DAAD94/NDP46-*********-x86-x64-AllOS-ENU.exe'
};

const temp = {
  'NSO': `${appData}/temp/artifacts-newso-%s.zip`,  // Make sure this matches what's used in the installer
  'NSO': `${appData}/temp/artifacts-freeso-%s.zip`,
  'MacExtras': `${appData}/temp/macextras-%s.zip`,
  'Mono': `${appData}/temp/mono-%s.pkg`,
  'RMS': `${appData}/temp/artifacts-remeshes-%s.zip`,
  'SDL': `${appData}/temp/sdl2-%s.dmg`,
  'TSO': {
    path: `${appData}/temp/tsoclient`,
    file: 'client.zip',
    extractionFolder: 'client',
    firstCab: 'TSO_Installer_v1.1239.1.0/Data1.cab',
    rootCab: 'Data1.cab'
  }
};

const registry = {
  ociName: 'NewSO Game',
  paths: {
    'TSO': process.platform === 'win32' ?
      'HKLM\\SOFTWARE\\Maxis\\The Sims Online' :
      `${appData}/GameComponents/The Sims Online/TSOClient/TSOClient.exe`,
    'NSO': process.platform === 'win32' ?
      'HKLM\\SOFTWARE\\Professor Oak\\NewSO' : 
      path.join(appData, 'GameComponents', 'NewSO', 'NewSO.exe'),
    'OpenAL': 'HKLM\\SOFTWARE\\OpenAL',
    'NET': 'HKLM\\SOFTWARE\\Microsoft\\NET Framework Setup\\NDP',
    'Mono': process.platform === 'darwin' ? '/Library/Frameworks/Mono.framework' : '/usr/bin/mono',
    'SDL': process.platform === 'darwin' ? '/Library/Frameworks/SDL2.framework' : `${linuxLibPath}/libSDL2-2.0.so.0`
  },
  fallbacks: process.platform === 'win32' ? {
    'TSO': [
      'C:/Program Files/Maxis/The Sims Online/TSOClient/TSOClient.exe',
      'C:/Program Files/The Sims Online/TSOClient/TSOClient.exe',
      'C:/Program Files/newSO Game/The Sims Online/TSOClient/TSOClient.exe'
    ],
    'NSO': [
      path.join('C:', 'Program Files', 'NewSO Game', 'NewSO', 'NewSO.exe'),
      path.join('C:', 'Program Files', 'NewSO', 'NewSO.exe')
    ],
    'OpenAL': [
      'C:/Program Files (x86)/OpenAL'
    ]
  } : {
    'TSO': [
      `${appData}/GameComponents/The Sims Online/TSOClient/TSOClient.exe`,
      `${homeDir}/Documents/The Sims Online/TSOClient/TSOClient.exe`,
    ],
    'NSO': [
      path.join(appData, 'GameComponents', 'NewSO', 'NewSO.exe'),
      path.join(homeDir, 'Documents', 'NewSO', 'NewSO.exe'),
    ]
  }
};

const gameLanguages = {
    English: 0,
    French: 3,
    German: 4,
    Italian: 5,
    Spanish: 6,
    Dutch: 7,
    Danish: 8,
    Swedish: 9,
    Norwegian: 10,
    Finnish: 11,
    Hebrew: 12,
    Russian: 13,
    Portuguese: 14,
    Japanese: 15,
    Polish: 16,
    SimplifiedChinese: 17,
    TraditionalChinese: 18,
    Thai: 19,
    Korean: 20,
    Slovak: 21
};

module.exports = {
  path,  // Export path module
  homeDir,
  appData,
  isTestMode,
  fileLogEnabled,
  devToolsEnabled,
  version,
  defaultRefreshRate,
  defaultGameLanguage,
  dependencies,
  needInternet,
  darkThemes,
  components,
  versionChecks,
  links,
  releases,
  resourceCentral,
  temp,
  registry,
  gameLanguages
};





