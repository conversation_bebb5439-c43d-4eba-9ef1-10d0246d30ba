const packager = require('@electron/packager').packager;
const { execSync } = require('child_process');
const path = require('path');

(async () => {
  try {
    // Rebuild native modules before packaging
    console.log('Rebuilding native modules...');
    execSync('npm rebuild macos-alias', { stdio: 'inherit' });

    console.log('Packaging application...');
    await packager({
      dir: '.',
      name: 'NewSO Launcher',
      out: '../release',
      platform: 'darwin',
      arch: 'universal',
      icon: './beta.icns',
      asar: {
        unpackDir: '{fsolauncher-ui/images,fsolauncher-ui/sounds,fsolauncher-ui/fonts}',
      },
      overwrite: true,
      appCopyright: 'Copyright (C) NewSO. All rights reserved.',
      derefSymlinks: true
    });

    console.log('Creating DMG...');
    // Properly escape paths with quotes and spaces
    const appPath = path.resolve('../release/NewSO Launcher-darwin-universal/NewSO Launcher.app');
    const outPath = path.resolve('../release');
    const iconPath = path.resolve('./beta.icns');
    const bgPath = path.resolve('./osx_dmg.png');

    const dmgArgs = [
      `"${appPath}"`,
      `"NewSO Launcher"`,
      `--out="${outPath}"`,
      `--icon="${iconPath}"`,
      `--background="${bgPath}"`,
      `--title="NewSO Launcher"`,
      `--overwrite`
    ];

    // Add additional options for Intel macs only
    if (process.arch !== 'arm64') {
      dmgArgs.push('--autoopen');
    }

    const command = `electron-installer-dmg ${dmgArgs.join(' ')}`;
    console.log('Executing command:', command);
    
    execSync(command, {
      stdio: 'inherit',
      shell: '/bin/bash'
    });
  } catch (err) {
    console.error('Build error:', err);
    process.exitCode = 1;
  }
})();
