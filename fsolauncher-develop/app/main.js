require('fix-path')(); // Fix $PATH on darwin
require('v8-compile-cache');

const path = require('path');
const fs = require('fs-extra');
const os = require('os');

// 🔧 Setup the Electron cache/userData path BEFORE requiring 'electron'
const userDataPath = path.join(os.homedir(), 'AppData', 'Roaming', 'NewSO_Launcher_UserData');
const cachePath = path.join(userDataPath, 'cache');

try {
  fs.ensureDirSync(userDataPath);
  fs.ensureDirSync(cachePath);
  process.env.ELECTRON_USER_DATA_PATH = userDataPath;
} catch (err) {
  console.error('Failed to create Electron data path:', err);
}

const {
  app,
  BrowserWindow,
  shell,
  Tray,
  Menu,
  nativeImage,
  nativeTheme
} = require('electron');

const { initSentry, enableFileLogger } = require('./fsolauncher/lib/utils');

// Disable GPU cache if we still have issues
app.commandLine.appendSwitch('disable-gpu-cache');

// init Sentry error logging as soon as possible
initSentry();

const compilePugFiles = require('./fsolauncher/lib/pug-compiler');

const {
  appData,
  version,
  darkThemes,
  resourceCentral,
  isTestMode,
  fileLogEnabled,
  devToolsEnabled,
  defaultRefreshRate,
  defaultGameLanguage,
  homeDir
} = require('./fsolauncher/constants');

if (fileLogEnabled) {
  enableFileLogger();
  console.info('file logger enabled');
}

if (isTestMode && process.platform !== 'linux') {
  app.disableHardwareAcceleration();
  app.commandLine.appendSwitch('no-sandbox');
  app.commandLine.appendSwitch('disable-gpu');
  app.commandLine.appendSwitch('disable-software-rasterizer');
  app.commandLine.appendSwitch('disable-gpu-compositing');
  app.commandLine.appendSwitch('disable-gpu-rasterization');
  app.commandLine.appendSwitch('disable-gpu-sandbox');
  app.commandLine.appendSwitch('--no-sandbox');
}

const { locale, setLocale } = require('./fsolauncher/lib/locale');

const oslocale = require('os-locale'),
  ini = require('ini');

const FSOLauncher = require('./fsolauncher/fsolauncher');

process.title = 'NewSO Launcher';

global.willQuit = false;

const prevOpenExternal = shell.openExternal;
shell.openExternal = Object.freeze(url => {
  if (url.startsWith('http') || url.startsWith('https')) {
    prevOpenExternal(url);
  }
});
Object.freeze(shell);

fs.ensureDirSync(appData + '/temp');

let window;
let tray;
let launcher;
let trayIcon;
let userSettings;

try {
  userSettings = ini.parse(fs.readFileSync(appData + '/FSOLauncher.ini', 'utf-8'));
} catch (err) {
  userSettings = {
    Launcher: {
      Theme: 'auto',
      DesktopNotifications: '1',
      Persistence: ['darwin', 'linux'].includes(process.platform) ? '0' : '1',
      DirectLaunch: '0',
      Language: 'default'
    },
    Game: {
      GraphicsMode: process.platform === 'win32' ? 'dx' : 'ogl',
      Language: defaultGameLanguage
    }
  };
  fs.writeFileSync(appData + '/FSOLauncher.ini', ini.stringify(userSettings), 'utf-8');
}
console.info('loaded userSettings', userSettings);

function loadLocale(settings) {
  let langCode = settings.Launcher.Language;
  if (!langCode || langCode === 'default') {
    langCode = oslocale.sync().substring(0, 2);
  }
  setLocale(langCode, {
    CSP_STRING: require('./csp.config'),
    LAUNCHER_VERSION: version,
    ELECTRON_VERSION: process.versions.electron,
    LAUNCHER_THEME: settings.Launcher.Theme === 'auto' ? nativeTheme.shouldUseDarkColors ? 'dark' : 'open_beta' : settings.Launcher.Theme,
    PLATFORM: process.platform,
    DARK_THEMES: darkThemes.join(','),
    SENTRY: require('./sentry.config').browserLoader,
    LANG_CODE: langCode,
    DEFAULT_REFRESH_RATE: defaultRefreshRate,
    REMESH_PACKAGE_CREDITS: require('./fsolauncher-ui/remesh-package.json'),
    PRELOADED_FONTS: require('./fonts.config'),
    WS_URL: resourceCentral.WS,
    TRENDING_LOTS_URL: resourceCentral.TrendingLots,
    SCENARIOS_URL: resourceCentral.Scenarios,
    SIMITONE_PLATFORM_PATH: appData.replace(homeDir, '~') + '/GameComponents/The Sims',
    BLOG_URL: resourceCentral.Blog
  });
}
loadLocale(userSettings);

const options = {};

function showWindow() {
  !isTestMode && window.show();
}

async function createWindow() {
  compilePugFiles({ pretty: false }, () => locale.current);

  trayIcon = nativeImage.createFromPath(
    path.join(__dirname, ['darwin', 'linux'].includes(process.platform) ? 'beta.png' : 'beta.ico')
  );
  if (['darwin', 'linux'].includes(process.platform)) {
    trayIcon = trayIcon.resize({ width: 16, height: 16 });
  }
  tray = new Tray(trayIcon);

  const width = 1090 + 8;
  const height = 646 + 12 + 30;

  Object.assign(options, {
    transparent: true,
    minWidth: width,
    minHeight: height,
    maxWidth: width,
    maxHeight: height,
    center: true,
    maximizable: false,
    width: width,
    height: height,
    useContentSize: true,
    show: false,
    frame: false,
    resizable: false,
    title: 'NewSO Launcher ' + version,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      offscreen: isTestMode,
      preload: path.join(__dirname, './fsolauncher-ui/preload.js')
    }
  });

  window = new BrowserWindow(options);
  window.setMenu(null);

  if (devToolsEnabled && !isTestMode) {
    console.info('devtools enabled');
    window.openDevTools({ mode: 'detach' });
  }

  window.loadURL(`file://${__dirname}/fsolauncher-ui/fsolauncher.pug`);

  window.on('hide', () => process.platform === 'win32' && window.setSize(width, height));
  window.on('restore', () => process.platform === 'win32' && window.setSkipTaskbar(false));

  launcher = new FSOLauncher({
    window,
    userSettings,
    onReload: async settings => {
      loadLocale(settings);
      window.reload();
    }
  });

  if (process.platform === 'darwin') {
    const darwinAppMenu = require('./fsolauncher/lib/darwin-app-menu');
    Menu.setApplicationMenu(Menu.buildFromTemplate(darwinAppMenu(app.getName(), launcher)));
  }

  tray.setToolTip(`NewSO Launcher ${version}`);
  tray.setContextMenu(Menu.buildFromTemplate([
    {
      label: locale.current.TRAY_LABEL_1,
      click: () => launcher.launchGame()
    },
    { type: 'separator' },
    {
      label: locale.current.TRAY_LABEL_2,
      click: () => {
        global.willQuit = true;
        window?.close();
      }
    }
  ]));

  tray.on('click', () => {
    if (!window) return;
    if (window.isVisible()) {
      if (['darwin', 'linux'].includes(process.platform)) {
        window.minimize();
      } else {
        window.hide();
      }
    } else {
      showWindow();
    }
  });

  window.on('closed', () => window = null);

  window.once('ready-to-show', () => {
    launcher.updateInstalledPrograms().then(() => {
      if (userSettings.Launcher.DirectLaunch === '1' && launcher.isInstalled.NSO) {
        launcher.launchGame();
        if (['darwin', 'linux'].includes(process.platform)) {
          showWindow();
        }
      } else {
        showWindow();
      }
    }).catch(err => {
      console.error(err);
      showWindow();
    });
  });

  window.on('close', e => {
    if (!global.willQuit && launcher.userSettings.Launcher.Persistence === '1') {
      e.preventDefault();
      window.minimize();
    }
  });

  window.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });
}

app.on('ready', createWindow);

app.on('before-quit', () => tray && tray.destroy());

app.on('window-all-closed', () => app.quit());

app.on('activate', () => window === null && createWindow());

const gotTheLock = app.requestSingleInstanceLock();
if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', (_event, _commandLine, _workingDirectory) => {
    if (window) {
      showWindow();
      window.focus();
    }
  });
}
